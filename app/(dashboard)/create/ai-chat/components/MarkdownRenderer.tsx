"use client";
import { useMemo } from "react";
import type { CodeToHtmlOptions } from "@llm-ui/code";
import {
  codeBlockLookBack,
  findCompleteCodeBlock,
  findPartialCodeBlock,
  loadHighlighter,
  useCodeBlockToHtml,
  allLangs,
  allLangsAlias,
} from "@llm-ui/code";
import { markdownLookBack } from "@llm-ui/markdown";
import { useLLMOutput, type LLMOutputComponent } from "@llm-ui/react";
import parseHtml from "html-react-parser";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { createHighlighter } from "shiki";
import { bundledThemes } from "shiki/themes";
import { bundledLanguagesInfo } from "shiki/langs";
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

// Markdown component with custom styling
const MarkdownComponent: LLMOutputComponent = ({ blockMatch }) => {
  const markdown = blockMatch.output;
  return (
    <div className="prose prose-sm max-w-none text-[15px] leading-relaxed">
      <ReactMarkdown 
        remarkPlugins={[remarkGfm]}
        components={{
          // Customize markdown elements
          h1: ({ children }) => <h1 className="text-lg font-semibold mb-2 text-gray-900">{children}</h1>,
          h2: ({ children }) => <h2 className="text-base font-semibold mb-2 text-gray-900">{children}</h2>,
          h3: ({ children }) => <h3 className="text-sm font-semibold mb-1 text-gray-900">{children}</h3>,
          p: ({ children }) => <div className="mb-2 text-gray-800">{children}</div>,
          ul: ({ children }) => <ul className="list-disc list-inside mb-2 text-gray-800">{children}</ul>,
          ol: ({ children }) => <ol className="list-decimal list-inside mb-2 text-gray-800">{children}</ol>,
          li: ({ children }) => <li className="mb-1">{children}</li>,
          code: ({ children }) => (
            <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-900">
              {children}
            </code>
          ),
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-gray-300 pl-4 mb-2 text-gray-700 italic">
              {children}
            </blockquote>
          ),
          a: ({ href, children }) => (
            <a href={href} className="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer">
              {children}
            </a>
          ),
          strong: ({ children }) => <strong className="font-semibold text-gray-900">{children}</strong>,
          em: ({ children }) => <em className="italic text-gray-800">{children}</em>,
          img: ({ src, alt }) => (
            <div className="my-3 max-w-md">
              <Dialog>
                <DialogTrigger asChild>
                  <div className="cursor-pointer hover:opacity-80 transition-opacity">
                    <div className="bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={src}
                        alt={alt || "Image"}
                        className="w-full h-auto object-contain max-h-96"
                        loading="lazy"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.parentElement!.innerHTML = `
                            <div class="flex items-center justify-center h-32 text-gray-500">
                              <span class="text-sm">Unable to load image</span>
                            </div>
                          `;
                        }}
                      />
                    </div>
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-7xl max-h-[95vh] p-0 border-0 bg-black/90 [&>button]:bg-white/20 [&>button]:text-white [&>button]:hover:bg-white/30 [&>button]:border [&>button]:border-white/30">
                  <DialogTitle className="sr-only">Image Viewer</DialogTitle>
                  <div className="flex items-center justify-center w-full h-full min-h-[50vh]">
                    <img
                      src={src}
                      alt={alt || "Image"}
                      className="max-w-full max-h-[95vh] object-contain"
                      loading="lazy"
                    />
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          ),
        }}
      >
        {markdown}
      </ReactMarkdown>
    </div>
  );
};

// Code block highlighter setup
const highlighter = loadHighlighter(
  createHighlighter({
    langs: allLangs(bundledLanguagesInfo),
    langAlias: allLangsAlias(bundledLanguagesInfo),
    themes: Object.values(bundledThemes),
  }),
);

const codeToHtmlOptions: CodeToHtmlOptions = {
  theme: "github-light",
};

// Code block component with custom styling
const CodeBlock: LLMOutputComponent = ({ blockMatch }) => {
  const { html, code } = useCodeBlockToHtml({
    markdownCodeBlock: blockMatch.output,
    highlighter,
    codeToHtmlOptions,
  });
  
  if (!html) {
    // Fallback to basic styling if Shiki is not loaded yet
    return (
      <div className="my-2 rounded-lg bg-gray-50 border border-gray-200 overflow-hidden">
        <pre className="p-4 text-sm font-mono text-gray-800 overflow-x-auto">
          <code>{code}</code>
        </pre>
      </div>
    );
  }
  
  return (
    <div className="my-2 rounded-lg overflow-hidden border border-gray-200">
      {parseHtml(html)}
    </div>
  );
};

interface MarkdownRendererProps {
  content: string;
  isStreamFinished?: boolean;
}

// Helper function to detect standalone image URLs
const processContentWithImages = (content: string) => {
  // Regex to find standalone image URLs (not already in markdown format)
  const imageUrlRegex = /(?<!\]\()(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp|svg))(?!\))/gi;
  
  // Replace standalone image URLs with markdown image syntax
  return content.replace(imageUrlRegex, (url) => {
    // Check if this URL is already part of markdown syntax
    const beforeUrl = content.substring(0, content.indexOf(url));
    if (beforeUrl.endsWith('](') || beforeUrl.endsWith('![')) {
      return url; // Don't modify if it's already markdown
    }
    return `![Image](${url})`;
  });
};

// Main markdown renderer component
export default function MarkdownRenderer({ content, isStreamFinished = true }: MarkdownRendererProps) {
  // Process content to convert standalone image URLs to markdown
  // Memoize the processed content to avoid unnecessary recalculations during streaming
  const processedContent = useMemo(() => processContentWithImages(content), [content]);

  const { blockMatches } = useLLMOutput({
    llmOutput: processedContent,
    fallbackBlock: {
      component: MarkdownComponent,
      lookBack: markdownLookBack(),
    },
    blocks: [
      {
        component: CodeBlock,
        findCompleteMatch: findCompleteCodeBlock(),
        findPartialMatch: findPartialCodeBlock(),
        lookBack: codeBlockLookBack(),
      },
    ],
    isStreamFinished,
  });

  return (
    <div className="whitespace-pre-wrap">
      {blockMatches.map((blockMatch, index) => {
        const Component = blockMatch.block.component;
        return <Component key={index} blockMatch={blockMatch} />;
      })}
    </div>
  );
}
