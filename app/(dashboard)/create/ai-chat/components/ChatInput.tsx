"use client";

import { useState, useRef, useCallback } from 'react';

import { Textarea } from '@/components/ui/textarea';
import { Send, Image as ImageIcon, X } from 'lucide-react';

import { ContentItem } from '../types/conversation';

interface ChatInputProps {
  onSendMessage: (content: string | ContentItem[]) => void;
  onFileUpload: (files: File[]) => void;
  disabled?: boolean;
  isProcessing?: boolean;
  placeholder?: string;
  isMobile?: boolean;
}

export default function ChatInput({
  onSendMessage,
  onFileUpload,
  disabled = false,
  isProcessing = false,
  placeholder = "Tell me about your product or what you'd like to create...",
  isMobile = false
}: ChatInputProps) {
  const [message, setMessage] = useState('');
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [convertingImages, setConvertingImages] = useState<Set<number>>(new Set());
  const [conversionErrors, setConversionErrors] = useState<Map<number, string>>(new Map());
  const [serverProcessing, setServerProcessing] = useState<Set<number>>(new Set());
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle sending message
  const handleSend = useCallback(() => {
    if (!message.trim() && uploadedImages.length === 0) return;
    if (disabled) return;

    // Create OpenAI format content
    const content: ContentItem[] = [];
    
    // Add text if present
    if (message.trim()) {
      content.push({ type: 'text', text: message.trim() });
    }
    
    // Add images if present
    uploadedImages.forEach(imageUrl => {
      content.push({ 
        type: 'image_url', 
        image_url: { url: imageUrl } 
      });
    });
    
    // Send as array if mixed content, or string if text only
    const messageContent = content.length === 1 && content[0].type === 'text' 
      ? content[0].text! 
      : content;
    
    onSendMessage(messageContent);
    setMessage('');
    setUploadedImages([]);
    setPreviewUrls([]);
    setConvertingImages(new Set());
    setServerProcessing(new Set());
    setConversionErrors(new Map());
    
    // Reset textarea height to minimum
    if (textareaRef.current) {
      textareaRef.current.style.height = '24px';
    }
  }, [message, uploadedImages, onSendMessage, disabled]);

  // Handle Enter key
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  // Auto-resize textarea
  const handleTextareaChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // Auto-resize with minimum height
    const textarea = e.target;
    textarea.style.height = '24px'; // Reset to minimum height
    const newHeight = Math.max(24, Math.min(textarea.scrollHeight, 100));
    textarea.style.height = `${newHeight}px`;
  }, []);

  // Detect if format needs server-side conversion
  const needsServerSideConversion = (file: File): boolean => {
    const modernFormats = ['image/avif', 'image/heic', 'image/heif'];
    const fileExtension = file.name.toLowerCase().split('.').pop() || '';
    const modernExtensions = ['avif', 'heic', 'heif'];
    
    return modernFormats.includes(file.type.toLowerCase()) ||
           modernExtensions.includes(fileExtension);
  };

  // Server-side conversion fallback
  const convertViaServer = async (dataUrl: string, imageIndex?: number): Promise<string> => {
    console.log('[ChatInput] Attempting server-side conversion...');
    
    // Track server processing
    if (imageIndex !== undefined) {
      setServerProcessing(prev => new Set(prev).add(imageIndex));
    }
    
    try {
      const response = await fetch('/api/image-convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: dataUrl,
          targetFormat: 'jpeg'
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Server conversion failed');
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Server conversion failed');
      }
      
      console.log('[ChatInput] Server-side conversion successful:', {
        originalFormat: result.originalFormat,
        convertedFormat: result.convertedFormat
      });
      
      return result.dataUrl;
    } catch (error) {
      console.error('[ChatInput] Server-side conversion failed:', error);
      
      // Track error
      if (imageIndex !== undefined) {
        setConversionErrors(prev => new Map(prev).set(imageIndex, error instanceof Error ? error.message : 'Server conversion failed'));
      }
      
      throw error;
    } finally {
      // Remove from server processing
      if (imageIndex !== undefined) {
        setServerProcessing(prev => {
          const newSet = new Set(prev);
          newSet.delete(imageIndex);
          return newSet;
        });
      }
    }
  };

  // Enhanced client-side conversion with timeout and fallback
  const convertImageClientSide = async (dataUrl: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      // Timeout to detect hanging promises (5 seconds)
      const timeoutId = setTimeout(() => {
        console.warn('[ChatInput] Client-side conversion timeout - browser cannot decode format');
        reject(new Error('Client-side conversion timeout'));
      }, 5000);
      
      img.onload = () => {
        clearTimeout(timeoutId);
        console.log('[ChatInput] Image loaded successfully for client-side conversion');

        try {
          // Create canvas for conversion
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            reject(new Error('Canvas context not available'));
            return;
          }
          
          // Set canvas size to image size (maintain original quality)
          canvas.width = img.naturalWidth;
          canvas.height = img.naturalHeight;
          
          // Draw image to canvas
          ctx.drawImage(img, 0, 0);
          
          // Convert to JPEG data URL (OpenAI-compatible)
          const convertedDataUrl = canvas.toDataURL('image/jpeg', 0.95);
          
          console.log('[ChatInput] Client-side conversion successful:', {
            dimensions: `${img.naturalWidth}x${img.naturalHeight}`,
            convertedSize: convertedDataUrl.length
          });
          
          resolve(convertedDataUrl);
        } catch (error) {
          console.error('[ChatInput] Canvas conversion error:', error);
          reject(error);
        }
      };
      
      img.onerror = () => {
        clearTimeout(timeoutId);
        console.warn('[ChatInput] Client-side image loading failed');
        reject(new Error('Client-side image loading failed'));
      };
      
      // Set image source to data URL
      img.src = dataUrl;
    });
  };

  // Convert image to OpenAI-compatible format with robust fallback
  const convertImageToOpenAIFormat = async (file: File, imageIndex?: number): Promise<string> => {
    console.log('[ChatInput] Starting conversion for file:', {
      name: file.name,
      type: file.type,
      size: file.size
    });

    // First, read the file as data URL
    const dataUrl = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const result = event.target?.result as string;
        console.log('[ChatInput] FileReader completed, data URL info:', {
          prefix: result.substring(0, 50),
          mimeType: result.match(/^data:([^;]+)/)?.[1],
          hasBase64Data: result.includes(',') && result.split(',')[1].length > 0
        });
        resolve(result);
      };
      
      reader.onerror = (error) => {
        console.error('[ChatInput] FileReader error:', error);
        reject(new Error('Failed to read image file'));
      };
      
      reader.readAsDataURL(file);
    });

    // Check if this format needs server-side processing
    if (needsServerSideConversion(file)) {
      console.log('[ChatInput] Modern format detected, using server-side conversion');
      try {
        return await convertViaServer(dataUrl, imageIndex);
      } catch (error) {
        console.error('[ChatInput] Server-side conversion failed, format may be unsupported:', error);
        throw new Error(`Format ${file.type} is not supported. Server-side conversion failed: ${error}`);
      }
    }

    // Try client-side conversion for traditional formats
    console.log('[ChatInput] Attempting client-side conversion');
    try {
      return await convertImageClientSide(dataUrl);
    } catch (clientError) {
      console.warn('[ChatInput] Client-side conversion failed, falling back to server:', clientError);
      
      // Fallback to server-side conversion
      try {
        return await convertViaServer(dataUrl, imageIndex);
      } catch (serverError) {
        console.error('[ChatInput] Both client and server conversion failed:', serverError);
        throw new Error(`Image conversion failed. Client-side: ${clientError}. Server-side: ${serverError}`);
      }
    }
  };

  // Handle file selection
  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files) return;

    console.log('[ChatInput] Starting file selection with', files.length, 'files');

    // Process files sequentially to avoid state conflicts
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        console.log('[ChatInput] Processing file:', {
          name: file.name,
          type: file.type,
          size: file.size,
          lastModified: file.lastModified
        });

        // Validate file type - check both MIME type and extension
        const supportedTypes = [
          'image/png', 'image/jpeg', 'image/jpg', 'image/webp', 'image/gif',
          'image/avif', 'image/heic', 'image/heif', 'image/tiff', 'image/tif', 'image/bmp'
        ];
        const supportedExtensions = [
          'png', 'jpg', 'jpeg', 'webp', 'gif',
          'avif', 'heic', 'heif', 'tiff', 'tif', 'bmp'
        ];
        
        const fileExtension = file.name.toLowerCase().split('.').pop() || '';
        const hasValidExtension = supportedExtensions.includes(fileExtension);
        const hasValidMimeType = supportedTypes.includes(file.type.toLowerCase());

        console.log('[ChatInput] File validation:', {
          extension: fileExtension,
          hasValidExtension,
          hasValidMimeType,
          mimeType: file.type
        });

        if (!hasValidMimeType && !hasValidExtension) {
          console.warn('[ChatInput] File rejected - invalid type and extension:', file.name);
          continue;
        }

        // Validate file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
          console.warn('[ChatInput] File rejected - too large:', file.name, file.size);
          continue;
        }

        console.log('[ChatInput] File passed validation, starting conversion...');
        
        // Get the current index for this image
        const imageIndex = previewUrls.length;
        
        // Show temporary preview immediately with loading state
        const tempPreviewUrl = URL.createObjectURL(file);
        
        console.log('[ChatInput] Adding temporary preview at index:', imageIndex);
        
        // Add to state with loading indicator
        setPreviewUrls(prev => [...prev, tempPreviewUrl]);
        setConvertingImages(prev => new Set(prev).add(imageIndex));
        
        try {
          console.log('[ChatInput] Converting image to OpenAI format...');
          // Convert to OpenAI-compatible format with image index for error tracking
          const convertedDataUrl = await convertImageToOpenAIFormat(file, imageIndex);
          
          console.log('[ChatInput] Conversion successful, updating states...');
          
          // Clear any previous errors for this image
          setConversionErrors(prev => {
            const newErrors = new Map(prev);
            newErrors.delete(imageIndex);
            return newErrors;
          });
          
          // Update with converted image and remove loading state
          setUploadedImages(prev => [...prev, convertedDataUrl]);
          setPreviewUrls(prev => {
            const newPreviews = [...prev];
            // Clean up temporary blob URL
            URL.revokeObjectURL(newPreviews[imageIndex]);
            // Replace with converted image
            newPreviews[imageIndex] = convertedDataUrl;
            return newPreviews;
          });
          setConvertingImages(prev => {
            const newSet = new Set(prev);
            newSet.delete(imageIndex);
            return newSet;
          });
          
          console.log('[ChatInput] File processing completed successfully');
          
          // Call the upload callback
          onFileUpload([file]);
          
        } catch (conversionError) {
          console.error('[ChatInput] Image conversion failed:', conversionError);
          
          // Store the error message
          const errorMessage = conversionError instanceof Error ? conversionError.message : 'Unknown conversion error';
          setConversionErrors(prev => new Map(prev).set(imageIndex, errorMessage));
          
          // Remove the failed image from previews and loading state
          setPreviewUrls(prev => {
            const newPreviews = [...prev];
            URL.revokeObjectURL(newPreviews[imageIndex]);
            newPreviews.splice(imageIndex, 1);
            return newPreviews;
          });
          setConvertingImages(prev => {
            const newSet = new Set(prev);
            newSet.delete(imageIndex);
            return newSet;
          });
        }
      } catch (fileError) {
        console.error('[ChatInput] File processing error:', fileError);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [previewUrls.length, onFileUpload]);



  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  // Remove uploaded image
  const removeImage = useCallback((index: number) => {
    setUploadedImages(prev => {
      const newImages = [...prev];
      // Clean up URLs if they're blob URLs (data URLs don't need cleanup)
      if (newImages[index].startsWith('blob:')) {
        URL.revokeObjectURL(newImages[index]);
      }
      newImages.splice(index, 1);
      return newImages;
    });
    setPreviewUrls(prev => {
      const newPreviews = [...prev];
      if (newPreviews[index].startsWith('blob:')) {
        URL.revokeObjectURL(newPreviews[index]);
      }
      newPreviews.splice(index, 1);
      return newPreviews;
    });
    // Clean up converting state
    setConvertingImages(prev => {
      const newSet = new Set([...prev].map(i => i > index ? i - 1 : i).filter(i => i !== index));
      return newSet;
    });
    // Clean up server processing state
    setServerProcessing(prev => {
      const newSet = new Set([...prev].map(i => i > index ? i - 1 : i).filter(i => i !== index));
      return newSet;
    });
    // Clean up conversion errors
    setConversionErrors(prev => {
      const newErrors = new Map();
      prev.forEach((error, idx) => {
        if (idx < index) {
          newErrors.set(idx, error);
        } else if (idx > index) {
          newErrors.set(idx - 1, error);
        }
        // Skip idx === index (remove that error)
      });
      return newErrors;
    });
  }, []);

  return (
    <div className={`${isMobile ? 'p-3 bg-white' : 'p-4'}`}>
      {/* Uploaded Images Preview */}
      {uploadedImages.length > 0 && (
        <div className="mb-3 flex flex-wrap gap-2">
          {previewUrls.map((previewUrl, index) => {
            const isConverting = convertingImages.has(index);
            const isServerProcessing = serverProcessing.has(index);
            const hasError = conversionErrors.has(index);
            const errorMessage = conversionErrors.get(index);
            
            return (
              <div key={index} className="relative">
                <div className="relative w-16 h-16">
                  <img
                    src={previewUrl}
                    alt={`Upload ${index + 1}`}
                    className={`w-16 h-16 object-cover rounded-lg border transition-opacity ${
                      hasError
                        ? 'border-red-300 opacity-75'
                        : isConverting || isServerProcessing
                        ? 'border-gray-200 opacity-50'
                        : 'border-gray-200 opacity-100'
                    }`}
                  />
                  {(isConverting || isServerProcessing) && (
                    <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-50 rounded-lg">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      {isServerProcessing && (
                        <div className="text-xs text-white mt-1 text-center">Server</div>
                      )}
                    </div>
                  )}
                  {hasError && (
                    <div className="absolute inset-0 flex items-center justify-center bg-red-500 bg-opacity-75 rounded-lg">
                      <div className="text-xs text-white text-center p-1">
                        Error
                      </div>
                    </div>
                  )}
                </div>
                <button
                  onClick={() => removeImage(index)}
                  disabled={isConverting || isServerProcessing}
                  className="absolute -top-2 -right-2 w-5 h-5 bg-gray-600 text-white rounded-full flex items-center justify-center hover:bg-black transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <X size={12} />
                </button>
                {hasError && errorMessage && (
                  <div className="absolute -bottom-6 left-0 right-0 text-xs text-red-600 text-center truncate">
                    {errorMessage.length > 20 ? errorMessage.substring(0, 20) + '...' : errorMessage}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      {/* Input Area */}
      <div
        className={`
          relative bg-white rounded-3xl border transition-all duration-200
          ${isMobile 
            ? 'shadow-[0_-4px_20px_rgba(0,0,0,0.15)] border-gray-200 mx-2 mb-2' 
            : 'shadow-sm border-gray-300'
          }
          ${isDragOver
            ? 'border-blue-400 bg-blue-50'
            : isMobile
              ? 'focus-within:border-blue-400 focus-within:shadow-[0_-6px_30px_rgba(0,0,0,0.2)]'
              : 'hover:border-gray-400 focus-within:border-blue-400 focus-within:shadow-md'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Drag Overlay */}
        {isDragOver && (
          <div className="absolute inset-0 bg-blue-50 bg-opacity-90 flex items-center justify-center rounded-3xl z-10">
            <div className="text-center">
              <ImageIcon className="w-6 h-6 text-blue-600 mx-auto mb-1" />
              <div className="text-sm font-medium text-blue-700">Drop images here</div>
            </div>
          </div>
        )}

        {/* Main Content Area - Mobile optimized layout */}
        <div className={`flex items-center gap-3 ${isMobile ? 'px-4 py-3 min-h-[56px]' : 'px-5 py-3'}`}>
          {/* Upload Button - Mobile optimized */}
          <button
            type="button"
            disabled={disabled}
            onClick={() => fileInputRef.current?.click()}
            className={`flex-shrink-0 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-40 touch-manipulation ${
              isMobile ? 'p-2' : 'p-1.5'
            }`}
          >
            <ImageIcon size={isMobile ? 22 : 20} className="text-gray-500" />
          </button>

          {/* Textarea */}
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={handleTextareaChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className={`flex-1 resize-none border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-0 shadow-none outline-none px-0 py-0 max-h-[100px] placeholder:text-gray-400 leading-6 rounded-none ${
              isMobile ? 'min-h-[28px] text-base' : 'min-h-[24px] text-base'
            }`}
            rows={1}
          />

          {/* Send Button - Mobile optimized */}
          <button
            type="button"
            disabled={disabled || isProcessing || (!message.trim() && uploadedImages.length === 0)}
            onClick={handleSend}
            className={`flex-shrink-0 bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:opacity-50 rounded-full transition-colors touch-manipulation ${
              isMobile ? 'p-3' : 'p-2.5'
            }`}
          >
            <Send size={isMobile ? 18 : 16} className="text-gray-600" />
          </button>
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          className="hidden"
          onChange={(e) => handleFileSelect(e.target.files)}
        />
      </div>
    </div>
  );
}
