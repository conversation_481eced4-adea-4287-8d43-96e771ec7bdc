"use client";

import { useState, useCallback, useEffect, useRef } from 'react';
import { ConversationState, Message, ConversationSession, UserContext } from '../types/conversation';

const STORAGE_KEY = 'ai-chat-conversation';

const initialSession: ConversationSession = {
  generationStatus: 'idle',
  generatedImages: [],
  uploadedFiles: []
};

const initialState: ConversationState = {
  messages: [],
  currentSession: initialSession,
  isTyping: false,
  isConnected: true
};

// Load conversation from localStorage
const loadConversationFromStorage = (): ConversationState => {
  if (typeof window === 'undefined') return initialState;
  
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return initialState;
    
    const parsed = JSON.parse(stored);
    
    // Ensure the stored data has the correct structure
    return {
      ...initialState,
      ...parsed,
      // Always reset typing and connection state
      isTyping: false,
      isConnected: true,
      // Restore messages with proper Date objects
      messages: (parsed.messages || []).map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }))
    };
  } catch (error) {
    console.warn('[useConversation] Failed to load conversation from storage:', error);
    return initialState;
  }
};

// Save conversation to localStorage
const saveConversationToStorage = (state: ConversationState) => {
  if (typeof window === 'undefined') return;
  
  try {
    // Only save persistent data (exclude temporary states)
    const toSave = {
      messages: state.messages,
      currentSession: {
        ...state.currentSession,
        // Don't persist uploaded files (they're File objects)
        uploadedFiles: []
      },
      userContext: state.userContext
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(toSave));
  } catch (error) {
    console.warn('[useConversation] Failed to save conversation to storage:', error);
  }
};

export function useConversation() {
  const [state, setState] = useState<ConversationState>(() => loadConversationFromStorage());
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Save to localStorage whenever messages change
  useEffect(() => {
    saveConversationToStorage(state);
  }, [state.messages, state.userContext]);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    // Use a small delay to ensure DOM updates are complete
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 50);
  }, []);

  useEffect(() => {
    // Only auto-scroll if we have messages
    if (state.messages.length > 0) {
      scrollToBottom();
    }
  }, [state.messages, scrollToBottom]);

  // Add a new message to the conversation
  const addMessage = useCallback((message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage]
    }));

    return newMessage.id;
  }, []);

  // Update a specific message (useful for loading states)
  const updateMessage = useCallback((messageId: string, updates: Partial<Message>) => {
    setState(prev => ({
      ...prev,
      messages: prev.messages.map(msg => 
        msg.id === messageId ? { ...msg, ...updates } : msg
      )
    }));
  }, []);

  // Set typing indicator
  const setTyping = useCallback((isTyping: boolean) => {
    setState(prev => ({ ...prev, isTyping }));
  }, []);

  // Update session data
  const updateSession = useCallback((updates: Partial<ConversationSession>) => {
    setState(prev => ({
      ...prev,
      currentSession: { ...prev.currentSession, ...updates }
    }));
  }, []);

  // Set user context
  const setUserContext = useCallback((userContext: UserContext) => {
    setState(prev => ({ ...prev, userContext }));
  }, []);

  // Add uploaded files to session
  const addUploadedFiles = useCallback((files: File[]) => {
    setState(prev => ({
      ...prev,
      currentSession: {
        ...prev.currentSession,
        uploadedFiles: [...prev.currentSession.uploadedFiles, ...files]
      }
    }));
  }, []);

  // Clear uploaded files
  const clearUploadedFiles = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentSession: {
        ...prev.currentSession,
        uploadedFiles: []
      }
    }));
  }, []);

  // Set product image in session
  const setProductImage = useCallback((imageUrl: string) => {
    setState(prev => ({
      ...prev,
      currentSession: {
        ...prev.currentSession,
        productImage: imageUrl
      }
    }));
  }, []);

  // Add generated images to session
  const addGeneratedImages = useCallback((images: any[]) => {
    setState(prev => ({
      ...prev,
      currentSession: {
        ...prev.currentSession,
        generatedImages: [...prev.currentSession.generatedImages, ...images],
        generationStatus: 'complete'
      }
    }));
  }, []);

  // Clear conversation (reset to initial state)
  const clearConversation = useCallback(() => {
    setState(initialState);
    // Also clear from localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEY);
    }
  }, []);

  // Get conversation history for API calls
  const getConversationHistory = useCallback(() => {
    return state.messages.map(msg => ({
      role: msg.type === 'user' ? 'user' : 'assistant',
      content: msg.content
    }));
  }, [state.messages]);

  return {
    // State
    messages: state.messages,
    currentSession: state.currentSession,
    userContext: state.userContext,
    isTyping: state.isTyping,
    isConnected: state.isConnected,
    
    // Actions
    addMessage,
    updateMessage,
    setTyping,
    updateSession,
    setUserContext,
    addUploadedFiles,
    clearUploadedFiles,
    setProductImage,
    addGeneratedImages,
    clearConversation,
    getConversationHistory,
    
    // Refs
    messagesEndRef
  };
}
