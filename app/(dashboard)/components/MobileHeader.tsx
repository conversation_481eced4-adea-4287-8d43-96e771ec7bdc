"use client";

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { Menu } from 'lucide-react';
import { useMobileNavigation } from '@/contexts/MobileNavigationContext';

interface MobileHeaderProps {
  className?: string;
}

export default function MobileHeader({ className = '' }: MobileHeaderProps) {
  const { toggleMenu } = useMobileNavigation();
  const [credits, setCredits] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch credits functionality
  const fetchCredits = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/credits');
      if (response.ok) {
        const data = await response.json();
        setCredits(data.credits);
      }
    } catch (error) {
      console.error('[MobileHeader] Error fetching credits:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCredits();

    // Listen for credit updates
    const handleCreditUpdate = () => {
      fetchCredits();
    };
    
    window.addEventListener('creditUpdated', handleCreditUpdate);
    
    return () => {
      window.removeEventListener('creditUpdated', handleCreditUpdate);
    };
  }, [fetchCredits]);

  return (
    <header className={`lg:hidden sticky top-0 z-30 border-b border-gray-200 bg-white ${className}`}>
      <div className="relative flex items-center justify-between px-4 py-3">
        {/* Hamburger Menu Button */}
        <button
          onClick={toggleMenu}
          className="p-2 hover:bg-gray-100 rounded-md transition-colors touch-manipulation z-10"
          aria-label="Open navigation menu"
        >
          <Menu size={24} className="text-gray-700" />
        </button>
        
        {/* Logo - Absolutely centered */}
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <Link href="/create">
            <h1 className="text-xl font-bold" style={{ color: '#FD2D55' }}>
              MARKET-ME
            </h1>
          </Link>
        </div>
        
        {/* Right side - Credits and Profile */}
        <div className="flex items-center space-x-2 z-10">
          {/* Credits display */}
          <Link
            href="/settings"
            className="flex items-center space-x-1 px-2 py-1 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors touch-manipulation"
          >
            <span className="text-base">💰</span>
            {loading ? (
              <span className="w-4 h-4 bg-gray-200 rounded-full animate-pulse"></span>
            ) : (
              <span className="font-medium text-sm">{credits}</span>
            )}
          </Link>
        </div>
      </div>
    </header>
  );
}
