{"epic_id": "mobile-chat-layout-fix", "title": "Mobile AI Chat Layout Fix", "description": "Fix mobile layout issue in AI chat sidebar where entire sidebar scrolls instead of having proper mobile chat interface", "status": "active", "priority": "high", "created": "2025-06-02T02:53:12Z", "estimated_completion": "2025-06-02T05:00:00Z", "tasks": [{"id": "TASK-001", "title": "Fix mobile layout structure in AIChatSidebar", "status": "pending", "priority": "high", "estimated_effort": "medium"}, {"id": "TASK-002", "title": "Optimize messages area scroll behavior", "status": "pending", "priority": "medium", "estimated_effort": "small"}, {"id": "TASK-003", "title": "Update ChatInput integration for flex layout", "status": "pending", "priority": "medium", "estimated_effort": "small"}, {"id": "TASK-004", "title": "Testing and validation across devices", "status": "pending", "priority": "high", "estimated_effort": "medium"}], "success_criteria": ["Mobile chat input stays fixed at bottom", "Only messages area scrolls on mobile", "Desktop layout unchanged", "All existing functionality preserved", "Works on iOS Safari and Android Chrome"], "related_documentation": [".project-tasks/requirements/prd/mobile_ai_chat_layout_fix_prd.md", ".project-tasks/requirements/research/codebase-analysis.md"]}